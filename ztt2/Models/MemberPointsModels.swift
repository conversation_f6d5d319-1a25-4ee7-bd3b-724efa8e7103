//
//  MemberPointsModels.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/30.
//

import Foundation

/**
 * 成员积分操作类型
 */
enum MemberPointsOperationType: String, CaseIterable {
    case add = "add"
    case deduct = "deduct"
    
    var displayName: String {
        switch self {
        case .add:
            return "加分"
        case .deduct:
            return "扣分"
        }
    }
    
    var colorHex: String {
        switch self {
        case .add:
            return "#26C34B"
        case .deduct:
            return "#FF5B5B"
        }
    }
    
    var iconName: String {
        switch self {
        case .add:
            return "plus.circle.fill"
        case .deduct:
            return "minus.circle.fill"
        }
    }
}

/**
 * 成员规则数据模型
 */
struct MemberRule: Identifiable, Codable {
    let id: UUID
    let name: String
    let value: Int
    let type: MemberPointsOperationType
    let createdAt: Date
    
    init(id: UUID = UUID(), name: String, value: Int, type: MemberPointsOperationType) {
        self.id = id
        self.name = name
        self.value = value
        self.type = type
        self.createdAt = Date()
    }
}

/**
 * 成员积分表单数据模型
 */
struct MemberPointsFormData {
    var items: [FormItem]
    let operationType: MemberPointsOperationType
    
    init(operationType: MemberPointsOperationType) {
        self.operationType = operationType
        self.items = [FormItem()]
    }
    
    /**
     * 表单项数据模型
     */
    struct FormItem: Identifiable {
        let id = UUID()
        var name: String = ""
        var value: String = ""
        var saveAsRule: Bool = false
        
        var isValid: Bool {
            return !name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
                   !value.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
                   Int(value) != nil &&
                   Int(value)! > 0
        }
    }
    
    // MARK: - 计算属性
    
    var validItems: [FormItem] {
        return items.filter { $0.isValid }
    }
    
    var hasValidData: Bool {
        return !validItems.isEmpty
    }
    
    var canAddMoreItems: Bool {
        return items.count < 5
    }
    
    var canDeleteItems: Bool {
        return items.count > 1
    }
    
    var totalPointsChange: Int {
        let total = validItems.compactMap { Int($0.value) }.reduce(0, +)
        return operationType == .add ? total : -total
    }
    
    // MARK: - 方法
    
    mutating func addNewItem() {
        if canAddMoreItems {
            items.append(FormItem())
        }
    }
    
    mutating func removeItem(at index: Int) {
        if canDeleteItems && index < items.count {
            items.remove(at: index)
        }
    }
    
    func validateItems() -> MemberPointsFormValidationResult {
        var errorMessages: [String] = []
        
        if items.isEmpty {
            errorMessages.append("至少需要添加一项操作")
        }
        
        if validItems.isEmpty {
            errorMessages.append("请填写完整的操作信息")
        }
        
        for (index, item) in items.enumerated() {
            if !item.name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                if item.value.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                    errorMessages.append("第\(index + 1)项缺少分值")
                } else if Int(item.value) == nil {
                    errorMessages.append("第\(index + 1)项分值格式不正确")
                } else if Int(item.value)! <= 0 {
                    errorMessages.append("第\(index + 1)项分值必须大于0")
                }
            }
        }
        
        return MemberPointsFormValidationResult(
            isValid: errorMessages.isEmpty,
            errorMessages: errorMessages
        )
    }
}

/**
 * 表单验证结果
 */
struct MemberPointsFormValidationResult {
    let isValid: Bool
    let errorMessages: [String]
}

/**
 * 积分操作记录
 */
struct MemberPointsOperation: Identifiable {
    let id = UUID()
    let memberId: String
    let name: String
    let value: Int
    let type: MemberPointsOperationType
    let timestamp: Date
    
    init(memberId: String, name: String, value: Int, type: MemberPointsOperationType) {
        self.memberId = memberId
        self.name = name
        self.value = value
        self.type = type
        self.timestamp = Date()
    }
}
