//
//  MemberPointsOptionsView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/30.
//

import SwiftUI

/**
 * 成员积分操作选项弹窗组件
 * 基于ztt1项目的StudentPointsOptionsView设计，适配家庭成员场景
 *
 * ## 功能特性
 * - 显示预设的加分/扣分规则列表
 * - 支持快速选择规则执行操作
 * - 支持自定义积分操作
 * - 支持添加新规则
 * - 美观的动画效果和触觉反馈
 *
 * ## 设计模式
 * 采用组合模式，将规则选择和自定义操作分离
 *
 * ## 兼容性
 * - iOS 15.6+
 * - 支持深色模式和动态字体
 * - 完整的无障碍支持
 */
struct MemberPointsOptionsView: View {

    // MARK: - Properties
    @Binding var isPresented: Bool
    let operationType: MemberPointsOperationType
    let rules: [MemberPointsRule]
    let onRuleSelected: (MemberPointsRule) -> Void
    let onCustomSelected: () -> Void
    let onAddRuleRequested: () -> Void
    let onRuleDeleted: ((MemberPointsRule) -> Void)?
    let onCancel: () -> Void
    
    // MARK: - State
    @State private var animationTrigger = false
    @State private var showFirstTimeGuide = false

    // MARK: - Body
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                            onCancel()
                        }
                    }
                    .transition(.opacity)
                
                // 选项菜单对话框
                GeometryReader { geometry in
                    VStack(spacing: 0) {
                        // 标题栏
                        headerView
                        
                        // 分隔线
                        dividerView
                        
                        // 常用规则列表区域
                        if !rules.isEmpty {
                            rulesSection
                        } else {
                            emptyRulesSection
                        }
                        
                        // 分隔线
                        if !rules.isEmpty {
                            dividerView
                        }
                        
                        // 自定义选项
                        customOptionSection
                        
                        // 底部间距
                        Spacer()
                            .frame(height: 10)
                    }
                    .frame(maxWidth: min(geometry.size.width - 40, 350))
                    .frame(maxHeight: min(geometry.size.height * 0.7, 500))
                    .background(Color.white)
                    .cornerRadius(20)
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(Color(hex: operationType.colorHex).opacity(0.2), lineWidth: 1.5)
                    )
                    .shadow(color: Color.black.opacity(0.1), radius: 15, x: 0, y: 8)
                    .scaleEffect(animationTrigger ? 1.0 : 0.9)
                    .opacity(animationTrigger ? 1.0 : 0.0)
                    .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
                }
            }
        }
        .animation(.easeInOut(duration: 0.25), value: isPresented)
        .onAppear {
            if isPresented {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }

                // 如果是首次使用且无规则，显示引导
                if rules.isEmpty {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                        showFirstTimeGuide = true
                    }
                }
            }
        }
        .onChange(of: isPresented) { newValue in
            if newValue {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
            } else {
                animationTrigger = false
            }
        }
        .alert("欢迎使用家庭成员规则", isPresented: $showFirstTimeGuide) {
            Button("开始配置") {
                showFirstTimeGuide = false
                onAddRuleRequested()
            }
            Button("稍后配置", role: .cancel) {
                showFirstTimeGuide = false
            }
        } message: {
            Text("您可以为家庭成员创建专属的\(operationType.displayName)规则，让积分管理更加个性化和高效。")
        }
        .alert("确认删除规则", isPresented: $showDeleteConfirmation) {
            Button("取消", role: .cancel) {
                ruleToDelete = nil
            }
            Button("删除", role: .destructive) {
                if let rule = ruleToDelete {
                    withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                        onRuleDeleted?(rule)
                    }

                    // 删除触觉反馈
                    let notificationFeedback = UINotificationFeedbackGenerator()
                    notificationFeedback.notificationOccurred(.success)
                }
                ruleToDelete = nil
            }
        } message: {
            if let rule = ruleToDelete {
                Text("确定要删除规则"\(rule.name)"吗？此操作无法撤销。")
            }
        }
    }
    
    // MARK: - 子视图组件
    
    /**
     * 标题栏
     */
    private var headerView: some View {
        HStack {
            Text(operationType.displayName + "选项")
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(DesignSystem.Colors.textPrimary)
            
            Spacer()
            
            // 关闭按钮
            Button(action: {
                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                    onCancel()
                }
            }) {
                Image(systemName: "xmark.circle.fill")
                    .font(.system(size: 24))
                    .foregroundColor(Color.gray.opacity(0.6))
            }
        }
        .padding(.horizontal, 20)
        .padding(.top, 20)
        .padding(.bottom, 16)
    }
    
    /**
     * 分隔线
     */
    private var dividerView: some View {
        Rectangle()
            .fill(Color(hex: "#edf5d9"))
            .frame(height: 1)
            .padding(.horizontal, 20)
    }
    
    /**
     * 常用规则列表区域
     */
    private var rulesSection: some View {
        VStack(spacing: 0) {
            // 区域标题
            HStack {
                Text("常用规则")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Spacer()

                // 添加规则按钮
                Button(action: {
                    // 触觉反馈
                    let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                    impactFeedback.impactOccurred()

                    onAddRuleRequested()
                }) {
                    Image(systemName: "plus.circle.fill")
                        .font(.system(size: 20))
                        .foregroundColor(Color(hex: operationType.colorHex))
                }
            }
            .padding(.horizontal, 20)
            .padding(.top, 16)
            .padding(.bottom, 12)
            
            // 规则列表
            ScrollView(.vertical, showsIndicators: false) {
                LazyVStack(spacing: 8) {
                    ForEach(rules) { rule in
                        MemberRuleOptionButton(
                            rule: rule,
                            operationType: operationType,
                            action: {
                                withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                                    onRuleSelected(rule)
                                }
                            }
                        )
                        .swipeActions(edge: .trailing, allowsFullSwipe: true) {
                            Button(role: .destructive) {
                                // 直接删除规则
                                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                    onRuleDeleted?(rule)
                                }

                                // 触觉反馈
                                let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                                impactFeedback.impactOccurred()
                            } label: {
                                Label("删除", systemImage: "trash")
                            }
                            .tint(.red)
                        }
                    }
                }
                .padding(.horizontal, 20)
            }
            .frame(maxHeight: 200)
        }
    }
    
    /**
     * 空规则状态
     */
    private var emptyRulesSection: some View {
        VStack(spacing: 12) {
            Image(systemName: "list.bullet")
                .font(.system(size: 32))
                .foregroundColor(Color.gray.opacity(0.5))

            Text("暂无\(operationType.displayName)规则")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(Color.gray)
                .multilineTextAlignment(.center)

            // 添加按钮
            Button(action: {
                // 触觉反馈
                let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                impactFeedback.impactOccurred()

                onAddRuleRequested()
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "plus.circle.fill")
                        .font(.system(size: 16))
                    Text("添加规则")
                        .font(.system(size: 14, weight: .medium))
                }
                .foregroundColor(Color(hex: operationType.colorHex))
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(Color(hex: operationType.colorHex).opacity(0.1))
                .cornerRadius(20)
            }
            .padding(.top, 8)
        }
        .padding(.vertical, 24)
    }
    
    /**
     * 自定义选项区域
     */
    private var customOptionSection: some View {
        MemberCustomOptionButton(
            operationType: operationType,
            action: {
                withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                    onCustomSelected()
                }
            }
        )
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
    }
}

/**
 * 成员规则选项按钮组件
 */
struct MemberRuleOptionButton: View {

    let rule: MemberPointsRule
    let operationType: MemberPointsOperationType
    let action: () -> Void

    @State private var isPressed = false

    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                // 左侧图标
                ZStack {
                    Circle()
                        .fill(Color(hex: operationType.colorHex).opacity(0.15))
                        .frame(width: 40, height: 40)

                    Image(systemName: operationType.iconName)
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(Color(hex: operationType.colorHex))
                }

                // 规则信息
                VStack(alignment: .leading, spacing: 4) {
                    Text(rule.name)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                        .multilineTextAlignment(.leading)

                    // 根据规则类型显示正确的符号和分值
                    Text("\(rule.type == .add ? "+" : "-")\(rule.value) 分")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color(hex: rule.type.colorHex))
                }

                Spacer()

                // 右侧箭头
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(Color.gray.opacity(0.6))
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(
                        isPressed
                        ? Color(hex: operationType.colorHex).opacity(0.1)
                        : Color.clear
                    )
            )
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color(hex: operationType.colorHex).opacity(0.2), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(.spring(response: 0.2, dampingFraction: 0.8), value: isPressed)
        .onTapGesture {
            // 触觉反馈
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred()
        }
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }
}

/**
 * 自定义选项按钮组件
 */
struct MemberCustomOptionButton: View {

    let operationType: MemberPointsOperationType
    let action: () -> Void

    @State private var isPressed = false

    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                // 左侧图标
                ZStack {
                    Circle()
                        .fill(Color(hex: "#f39c12").opacity(0.15))
                        .frame(width: 40, height: 40)

                    Image(systemName: "square.and.pencil")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(Color(hex: "#f39c12"))
                }

                // 自定义信息
                VStack(alignment: .leading, spacing: 4) {
                    Text("自定义\(operationType.displayName)")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textPrimary)

                    Text("输入自定义的\(operationType.displayName)原因和分值")
                        .font(.system(size: 14, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }

                Spacer()

                // 右侧箭头
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(Color.gray.opacity(0.6))
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(
                        isPressed
                        ? Color(hex: "#f39c12").opacity(0.1)
                        : Color.clear
                    )
            )
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color(hex: "#f39c12").opacity(0.3), lineWidth: 1.5)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(.spring(response: 0.2, dampingFraction: 0.8), value: isPressed)
        .onTapGesture {
            // 触觉反馈
            let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
            impactFeedback.impactOccurred()
        }
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }
}

// MARK: - Preview
#Preview {
    ZStack {
        Color.gray.opacity(0.3)
            .ignoresSafeArea()

        MemberPointsOptionsView(
            isPresented: .constant(true),
            operationType: .add,
            rules: [
                MemberPointsRule(name: "完成作业", value: 5, type: .add),
                MemberPointsRule(name: "帮助家务", value: 3, type: .add)
            ],
            onRuleSelected: { rule in
                print("选择规则: \(rule.name)")
            },
            onCustomSelected: {
                print("选择自定义")
            },
            onAddRuleRequested: {
                print("请求添加规则")
            },
            onRuleDeleted: { rule in
                print("删除规则: \(rule.name)")
            },
            onCancel: {
                print("取消操作")
            }
        )
    }
}
