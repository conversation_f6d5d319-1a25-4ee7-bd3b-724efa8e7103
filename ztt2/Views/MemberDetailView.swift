//
//  MemberDetailView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/29.
//

import SwiftUI

/**
 * 成员详情视图
 * 基于ztt1项目的StudentDetailView设计，适配家庭成员场景
 */
struct MemberDetailView: View {

    // MARK: - Properties
    let memberId: String?
    let onClose: () -> Void
    let onNavigateToSubscription: () -> Void

    // MARK: - Animation States
    @State private var pageAppeared = false

    // MARK: - Mock Data (临时数据，后续替换为真实数据)
    @State private var memberName = "多多"
    @State private var memberRole = "儿子"
    @State private var memberAge = 9
    @State private var currentPoints = 10
    @State private var selectedRecordType: RecordType = .points

    // MARK: - 弹窗状态管理
    @State private var showAddPointsOptions = false
    @State private var showDeductPointsOptions = false
    @State private var showAddPointsForm = false
    @State private var showDeductPointsForm = false

    // MARK: - 模拟规则数据
    @State private var addPointsRules: [MemberPointsRule] = [
        MemberPointsRule(id: UUID(), name: "完成作业", value: 5, type: .add),
        MemberPointsRule(id: UUID(), name: "帮助家务", value: 3, type: .add),
        MemberPointsRule(id: UUID(), name: "主动学习", value: 10, type: .add)
    ]

    @State private var deductPointsRules: [MemberPointsRule] = [
        MemberPointsRule(id: UUID(), name: "不听话", value: 5, type: .deduct),
        MemberPointsRule(id: UUID(), name: "乱发脾气", value: 3, type: .deduct),
        MemberPointsRule(id: UUID(), name: "不完成作业", value: 10, type: .deduct)
    ]

    // MARK: - Record Types
    enum RecordType: String, CaseIterable {
        case points = "积分记录"
        case exchange = "兑换记录"

        var displayName: String {
            return self.rawValue
        }
    }

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 美化背景渐变
                LinearGradient(
                    gradient: Gradient(stops: [
                        .init(color: Color(hex: "#fcfff4"), location: 0.0),
                        .init(color: Color(hex: "#f8fdf0"), location: 0.3),
                        .init(color: Color.white, location: 0.7),
                        .init(color: Color(hex: "#fafffe"), location: 1.0)
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea(.all)

                // 装饰性背景元素
                VStack {
                    HStack {
                        Spacer()
                        Circle()
                            .fill(Color(hex: "#B5E36B").opacity(0.04))
                            .frame(width: 100, height: 100)
                            .offset(x: 30, y: 20)
                    }
                    Spacer()
                    HStack {
                        Circle()
                            .fill(Color(hex: "#FFE49E").opacity(0.05))
                            .frame(width: 80, height: 80)
                            .offset(x: -40, y: -30)
                        Spacer()
                        Circle()
                            .fill(Color(hex: "#B5E36B").opacity(0.03))
                            .frame(width: 60, height: 60)
                            .offset(x: 20, y: 40)
                    }
                }

                VStack(spacing: 0) {
                    // 顶部关闭按钮
                    MemberDetailHeader {
                        onClose()
                    }
                    .offset(y: pageAppeared ? 0 : -80)
                    .animation(.easeInOut(duration: 0.6).delay(0.1), value: pageAppeared)

                    // 主要内容区域
                    VStack(spacing: DesignSystem.Spacing.lg) {
                        // 成员信息卡片
                        MemberInfoCard(
                            memberName: memberName,
                            memberRole: memberRole,
                            memberAge: memberAge,
                            currentPoints: currentPoints,
                            onAddPointsTapped: {
                                showAddPointsOptions = true
                            },
                            onDeductPointsTapped: {
                                showDeductPointsOptions = true
                            },
                            onExchangeTapped: {
                                // TODO: 实现兑换功能
                                print("兑换按钮点击")
                            },
                            onLotteryTapped: {
                                // TODO: 实现抽奖功能
                                print("抽奖按钮点击")
                            },
                            onAnalysisReportTapped: {
                                // TODO: 实现AI分析功能
                                print("AI分析按钮点击")
                            }
                        )
                        .padding(.horizontal, 25)
                        .opacity(pageAppeared ? 1.0 : 0.0)
                        .offset(y: pageAppeared ? 0 : 50)
                        .animation(.easeInOut(duration: 0.8).delay(0.1), value: pageAppeared)

                        // 历史记录组件
                        MemberHistoryRecordsView(selectedRecordType: $selectedRecordType)
                            .padding(.horizontal, 25)
                            .opacity(pageAppeared ? 1.0 : 0.0)
                            .offset(y: pageAppeared ? 0 : 30)
                            .animation(.easeInOut(duration: 0.6).delay(0.5), value: pageAppeared)

                        Spacer()  // 填充剩余空间
                    }
                    .padding(.top, DesignSystem.Spacing.sm)
                }
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            withAnimation(.easeInOut(duration: 0.8)) {
                pageAppeared = true
            }
        }
        // MARK: - 弹窗覆盖层
        .overlay(
            ZStack {
                // 加分选项弹窗
                if showAddPointsOptions {
                    MemberPointsOptionsView(
                        isPresented: $showAddPointsOptions,
                        operationType: .add,
                        rules: addPointsRules,
                        onRuleSelected: { rule in
                            // 直接执行加分操作
                            executePointsOperation(rule: rule)
                            showAddPointsOptions = false
                        },
                        onCustomSelected: {
                            showAddPointsOptions = false
                            showAddPointsForm = true
                        },
                        onAddRuleRequested: {
                            showAddPointsOptions = false
                            showAddPointsForm = true
                        },
                        onRuleDeleted: { rule in
                            // 删除加分规则
                            deleteRule(rule: rule, from: .add)
                        },
                        onCancel: {
                            showAddPointsOptions = false
                        }
                    )
                }

                // 扣分选项弹窗
                if showDeductPointsOptions {
                    MemberPointsOptionsView(
                        isPresented: $showDeductPointsOptions,
                        operationType: .deduct,
                        rules: deductPointsRules,
                        onRuleSelected: { rule in
                            // 直接执行扣分操作
                            executePointsOperation(rule: rule)
                            showDeductPointsOptions = false
                        },
                        onCustomSelected: {
                            showDeductPointsOptions = false
                            showDeductPointsForm = true
                        },
                        onAddRuleRequested: {
                            showDeductPointsOptions = false
                            showDeductPointsForm = true
                        },
                        onRuleDeleted: { rule in
                            // 删除扣分规则
                            deleteRule(rule: rule, from: .deduct)
                        },
                        onCancel: {
                            showDeductPointsOptions = false
                        }
                    )
                }

                // 加分表单弹窗
                if showAddPointsForm {
                    MemberPointsFormView(
                        isPresented: $showAddPointsForm,
                        operationType: .add,
                        onSubmit: { formData in
                            // 处理表单提交
                            handleFormSubmission(formData: formData, operationType: .add)
                            showAddPointsForm = false
                        },
                        onCancel: {
                            showAddPointsForm = false
                        }
                    )
                }

                // 扣分表单弹窗
                if showDeductPointsForm {
                    MemberPointsFormView(
                        isPresented: $showDeductPointsForm,
                        operationType: .deduct,
                        onSubmit: { formData in
                            // 处理表单提交
                            handleFormSubmission(formData: formData, operationType: .deduct)
                            showDeductPointsForm = false
                        },
                        onCancel: {
                            showDeductPointsForm = false
                        }
                    )
                }
            }
        )
    }

    // MARK: - 业务逻辑方法

    /**
     * 执行积分操作
     */
    private func executePointsOperation(rule: MemberPointsRule) {
        let change = rule.type == .add ? rule.value : -rule.value
        currentPoints += change

        // TODO: 这里应该调用数据管理器保存积分变更记录
        print("执行积分操作: \(rule.name), 变更: \(change), 当前积分: \(currentPoints)")

        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }

    /**
     * 处理表单提交
     */
    private func handleFormSubmission(formData: MemberPointsFormData, operationType: MemberPointsOperationType) {
        // 执行积分操作
        for item in formData.items {
            if !item.name.isEmpty && !item.value.isEmpty, let value = Int(item.value) {
                let change = operationType == .add ? value : -value
                currentPoints += change

                // 如果勾选了保存为规则，添加到对应的规则列表
                if item.saveAsRule {
                    let newRule = MemberPointsRule(
                        id: UUID(),
                        name: item.name,
                        value: value,
                        type: operationType
                    )

                    if operationType == .add {
                        addPointsRules.append(newRule)
                    } else {
                        deductPointsRules.append(newRule)
                    }
                }
            }
        }

        // TODO: 这里应该调用数据管理器保存所有变更
        print("处理表单提交: \(formData.items.count) 项操作，当前积分: \(currentPoints)")

        // 成功触觉反馈
        let notificationFeedback = UINotificationFeedbackGenerator()
        notificationFeedback.notificationOccurred(.success)
    }

    /**
     * 删除规则
     */
    private func deleteRule(rule: MemberPointsRule, from operationType: MemberPointsOperationType) {
        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
            if operationType == .add {
                addPointsRules.removeAll { $0.id == rule.id }
            } else {
                deductPointsRules.removeAll { $0.id == rule.id }
            }
        }

        // TODO: 这里应该调用数据管理器删除规则
        print("删除规则: \(rule.name) (\(operationType.displayName))")

        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }
}

// MARK: - Supporting Views

/**
 * 成员详情页顶部导航组件
 */
struct MemberDetailHeader: View {
    let onClose: () -> Void

    var body: some View {
        HStack {
            Spacer()

            // 关闭按钮 - 右上角
            Button(action: onClose) {
                Image("关闭")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 30, height: 30)
                    .foregroundColor(DesignSystem.Colors.textPrimary)
            }
            .frame(width: 44, height: 44)  // 扩大点击区域
            .background(Color.clear)
        }
        .padding(.horizontal, 25)  // 设置两侧边距为25pt
        .padding(.top, -10)  // 减少顶部间距
    }
}

/**
 * 成员信息卡片组件
 * 基于StudentInfoCard设计，适配家庭成员场景
 */
struct MemberInfoCard: View {

    let memberName: String
    let memberRole: String
    let memberAge: Int
    let currentPoints: Int
    let onAddPointsTapped: () -> Void
    let onDeductPointsTapped: () -> Void
    let onExchangeTapped: () -> Void
    let onLotteryTapped: () -> Void
    let onAnalysisReportTapped: () -> Void

    var body: some View {
        ZStack {
            // 卡片背景
            RoundedRectangle(cornerRadius: 25)
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color(hex: "#edf6d9"),
                            Color(hex: "#e8f1d4")
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .shadow(color: Color.black.opacity(0.08), radius: 8, x: 0, y: 4)

            // 装饰性背景元素
            Circle()
                .fill(Color(hex: "#B5E36B").opacity(0.08))
                .frame(width: 60, height: 60)
                .offset(x: 120, y: -80)

            Circle()
                .fill(Color(hex: "#FFE49E").opacity(0.06))
                .frame(width: 45, height: 45)
                .offset(x: -90, y: 60)

            Circle()
                .fill(Color(hex: "#74c07f").opacity(0.05))
                .frame(width: 35, height: 35)
                .offset(x: 100, y: 50)

            // 成员头像 - 左上角
            HStack {
                VStack {
                    ZStack {
                        // 头像背景光圈
                        Circle()
                            .fill(
                                RadialGradient(
                                    gradient: Gradient(colors: [
                                        Color(hex: "#a9d051").opacity(0.1),
                                        Color.clear
                                    ]),
                                    center: .center,
                                    startRadius: 0,
                                    endRadius: 50
                                )
                            )
                            .frame(width: 100, height: 100)

                        // 根据角色显示对应头像
                        Image(getAvatarImageName(for: memberRole))
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 80, height: 80)
                            .clipShape(Circle())
                    }
                    .padding(.top, DesignSystem.Spacing.md)
                    .padding(.leading, DesignSystem.Spacing.md)
                    Spacer()
                }
                Spacer()
            }

            // 成员信息 - 中上区域（头像右侧）
            HStack {
                Spacer()
                VStack(alignment: .leading, spacing: 4) {
                    // 成员姓名
                    Text(memberName)
                        .font(.system(size: 30, weight: .semibold))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                        .shadow(color: Color.white.opacity(0.5), radius: 1, x: 0, y: 1)
                        .padding(.top, DesignSystem.Spacing.md)

                    // 角色和年龄信息
                    Text("\(memberRole) · \(memberAge)岁")
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(DesignSystem.Colors.textSecondary)

                    Spacer()
                }
                Spacer()
            }

            // 积分显示 - 右下角
            HStack {
                Spacer()
                VStack {
                    Spacer()
                    ZStack {
                        // 积分背景光圈
                        Circle()
                            .fill(
                                RadialGradient(
                                    gradient: Gradient(colors: [
                                        Color(hex: "#74c07f").opacity(0.1),
                                        Color.clear
                                    ]),
                                    center: .center,
                                    startRadius: 0,
                                    endRadius: 60
                                )
                            )
                            .frame(width: 120, height: 120)

                        Text("\(currentPoints)")
                            .font(.custom("Impact", size: 90))
                            .foregroundColor(DesignSystem.Colors.scoreDisplay)
                            .shadow(color: Color(hex: "#74c07f").opacity(0.3), radius: 4, x: 0, y: 2)
                    }
                    .padding(.bottom, DesignSystem.Spacing.md)
                    .padding(.trailing, DesignSystem.Spacing.md)
                    .offset(y: 20)  // 向下移动20pt
                }
            }

            // 上排操作按钮 - 左下区域上方
            HStack {
                VStack {
                    Spacer()
                    MemberTopActionButtons(
                        onAddPointsTapped: onAddPointsTapped,
                        onDeductPointsTapped: onDeductPointsTapped,
                        buttonSize: 50,
                        spacing: 5,
                        cornerRadius: 5
                    )
                    .padding(.bottom, 70)  // 为下排按钮留出空间
                    .padding(.leading, DesignSystem.Spacing.md)
                }
                Spacer()
            }

            // 下排操作按钮 - 左下角
            HStack {
                VStack {
                    Spacer()
                    MemberBottomActionButtons(
                        memberRole: memberRole,
                        onExchangeTapped: onExchangeTapped,
                        onLotteryTapped: onLotteryTapped,
                        onAnalysisReportTapped: onAnalysisReportTapped,
                        buttonSize: 50,
                        spacing: 5,
                        cornerRadius: 5
                    )
                    .padding(.bottom, DesignSystem.Spacing.md)
                    .padding(.leading, DesignSystem.Spacing.md)
                }
                Spacer()
            }
        }
        .frame(height: 280)
    }

    /**
     * 根据角色获取头像图片名称
     */
    private func getAvatarImageName(for role: String) -> String {
        switch role {
        case "爸爸":
            return "爸爸头像"
        case "妈妈":
            return "妈妈头像"
        case "儿子":
            return "男生头像"
        case "女儿":
            return "女生头像"
        default:
            return "其他头像"
        }
    }
}

/**
 * 成员历史记录组件
 * 基于HistoryRecordsView设计，适配家庭成员场景
 */
struct MemberHistoryRecordsView: View {

    @Binding var selectedRecordType: MemberDetailView.RecordType
    @State private var recordsAppeared = false

    var body: some View {
        VStack(spacing: 0) {
            // 选项卡切换
            MemberRecordTabSelector(
                selectedType: $selectedRecordType,
                onSelectionChanged: { type in
                    selectedRecordType = type
                }
            )
            .opacity(recordsAppeared ? 1.0 : 0.0)
            .offset(y: recordsAppeared ? 0 : -20)
            .animation(.easeInOut(duration: 0.5).delay(0.1), value: recordsAppeared)

            // 记录列表
            MemberRecordsList(
                recordType: selectedRecordType,
                recordsAppeared: $recordsAppeared
            )
        }
        .background(
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(hex: "#f8f8f8"),
                    Color(hex: "#ededed")
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
        )
        .cornerRadius(25)
        .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
        .onAppear {
            withAnimation {
                recordsAppeared = true
            }
        }
    }
}

/**
 * 记录类型选择器
 */
struct MemberRecordTabSelector: View {
    @Binding var selectedType: MemberDetailView.RecordType
    let onSelectionChanged: (MemberDetailView.RecordType) -> Void

    var body: some View {
        HStack(spacing: 0) {
            ForEach(MemberDetailView.RecordType.allCases, id: \.self) { type in
                Button(action: {
                    selectedType = type
                    onSelectionChanged(type)
                }) {
                    VStack(spacing: 0) {
                        Text(type.displayName)
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(selectedType == type ? DesignSystem.Colors.textPrimary : DesignSystem.Colors.textSecondary)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                            .background(
                                RoundedRectangle(cornerRadius: 16)
                                    .fill(selectedType == type ? Color.white : Color.clear)
                            )

                        // 选中状态下显示绿色底线
                        if selectedType == type {
                            Rectangle()
                                .fill(Color(hex: "a9d051"))
                                .frame(width: 70, height: 5)
                                .cornerRadius(2)
                        }
                    }
                }
            }
        }
        .padding(.horizontal, DesignSystem.Spacing.md)
        .padding(.top, DesignSystem.Spacing.md)
        .padding(.bottom, DesignSystem.Spacing.sm)
    }
}

/**
 * 记录列表组件
 */
struct MemberRecordsList: View {
    let recordType: MemberDetailView.RecordType
    @Binding var recordsAppeared: Bool

    var body: some View {
        // 空状态显示
        MemberEmptyRecordsView()
    }
}

/**
 * 空状态视图
 */
struct MemberEmptyRecordsView: View {
    var body: some View {
        VStack(spacing: DesignSystem.Spacing.lg) {
            // 图标容器
            ZStack {
                Circle()
                    .fill(DesignSystem.Colors.historyBackground.opacity(0.6))
                    .frame(width: 80, height: 80)

                Image("lishi")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 40, height: 40)
                    .foregroundColor(DesignSystem.Colors.textSecondary.opacity(0.7))
            }

            VStack(spacing: 8) {
                Text("暂无记录")
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Text("学生的积分变动记录将在此显示")
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 60)
    }
}

/**
 * 上排操作按钮组件（加分、扣分）
 */
struct MemberTopActionButtons: View {

    let onAddPointsTapped: () -> Void
    let onDeductPointsTapped: () -> Void
    let buttonSize: CGFloat
    let spacing: CGFloat
    let cornerRadius: CGFloat

    var body: some View {
        HStack(spacing: spacing) {
            MemberCustomActionButton(
                title: "加分",
                systemIcon: "plus",
                size: buttonSize,
                cornerRadius: cornerRadius,
                action: onAddPointsTapped
            )

            MemberCustomActionButton(
                title: "扣分",
                systemIcon: "minus",
                size: buttonSize,
                cornerRadius: cornerRadius,
                action: onDeductPointsTapped
            )
        }
    }
}

/**
 * 下排操作按钮组件（兑换、抽奖、分析报告）
 */
struct MemberBottomActionButtons: View {

    let memberRole: String
    let onExchangeTapped: () -> Void
    let onLotteryTapped: () -> Void
    let onAnalysisReportTapped: () -> Void
    let buttonSize: CGFloat
    let spacing: CGFloat
    let cornerRadius: CGFloat

    var body: some View {
        HStack(spacing: spacing) {
            MemberCustomActionButton(
                title: "兑换",
                imageName: "lingqujilu",
                size: buttonSize,
                cornerRadius: cornerRadius,
                action: onExchangeTapped
            )

            MemberCustomActionButton(
                title: "抽奖",
                imageName: "choujiang",
                size: buttonSize,
                cornerRadius: cornerRadius,
                action: onLotteryTapped
            )

            // AI分析按钮只在角色为"儿子"或"女儿"时显示
            if shouldShowAnalysisButton {
                MemberCustomActionButton(
                    title: "分析",
                    imageName: "fenxi",
                    size: buttonSize,
                    cornerRadius: cornerRadius,
                    action: onAnalysisReportTapped
                )
            }
        }
    }

    /**
     * 判断是否应该显示AI分析按钮
     * 只有角色为"儿子"或"女儿"时才显示
     */
    private var shouldShowAnalysisButton: Bool {
        return memberRole == "儿子" || memberRole == "女儿"
    }
}

/**
 * 可配置的操作按钮组件
 */
struct MemberCustomActionButton: View {
    let title: String
    let systemIcon: String?
    let imageName: String?
    let size: CGFloat
    let cornerRadius: CGFloat
    let action: () -> Void

    @State private var isPressed = false

    init(title: String, systemIcon: String, size: CGFloat, cornerRadius: CGFloat, action: @escaping () -> Void) {
        self.title = title
        self.systemIcon = systemIcon
        self.imageName = nil
        self.size = size
        self.cornerRadius = cornerRadius
        self.action = action
    }

    init(title: String, imageName: String, size: CGFloat, cornerRadius: CGFloat, action: @escaping () -> Void) {
        self.title = title
        self.systemIcon = nil
        self.imageName = imageName
        self.size = size
        self.cornerRadius = cornerRadius
        self.action = action
    }

    var body: some View {
        Button(action: {
            // 触觉反馈
            let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
            impactFeedback.impactOccurred()

            // 按压动画
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = true
            }

            // 执行动作
            action()

            // 重置按压状态
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(.easeInOut(duration: 0.1)) {
                    isPressed = false
                }
            }
        }) {
            VStack(spacing: 4) {
                // 图标
                if let systemIcon = systemIcon {
                    Image(systemName: systemIcon)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                } else if let imageName = imageName {
                    Image(imageName)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 16, height: 16)
                        .foregroundColor(.white)
                }

                // 标题
                Text(title)
                    .font(.system(size: 10, weight: .medium))
                    .foregroundColor(.white)
                    .lineLimit(1)
            }
        }
        .frame(width: size, height: size)
        .background(DesignSystem.Colors.actionButtonBackground)
        .cornerRadius(cornerRadius)
        .scaleEffect(isPressed ? 0.95 : 1.0)
        .animation(.easeInOut(duration: 0.1), value: isPressed)
    }
}

// MARK: - Preview
#Preview {
    MemberDetailView(
        memberId: "test-member-id",
        onClose: {},
        onNavigateToSubscription: {}
    )
}
